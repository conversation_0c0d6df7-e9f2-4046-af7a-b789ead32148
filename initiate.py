import socket
import subprocess
import sys
import time
import requests

from ipc import IPC
from BASE.utils.platform_detector import PlatformDetector
from BASE.vdb.qdrant import get_qdrant_client, start_qdrant_server
from BASE.vdb.download import main as download_qdrant
import BASE.utils.check_cuda
from startup_config import perform_startup_configuration, read_session, session_refresh

# Initialize IPC
ipc_ = IPC.connect()

# Set initial states
ipc_.set("platform", PlatformDetector.get_os_name())
ipc_.set("ollama_running", False)
ipc_.set("internet_connected", False)
ipc_.set("cuda_available", False)
ipc_.set("current_session", None)
ipc_.set("extension_version", None)
ipc_.set("subsystem_version", None)


def is_port_open(host="localhost", port=11434, timeout=3):
    """Check if a port is open (service is running)."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            return result == 0
    except:
        return False

def check_ollama():
    """Check if Ollama is running."""
    running = is_port_open("localhost", 11434)
    ipc_.set("ollama_running", running)
    print(f"Ollama: {'Running' if running else 'Not running'}")

def check_internet():
    """Check internet connectivity."""
    try:
        response = requests.get("https://www.google.com", timeout=5)
        connected = response.status_code == 200
    except:
        connected = False
    
    ipc_.set("internet_connected", connected)
    print(f"Internet: {'Connected' if connected else 'Disconnected'}")

def setup_qdrant():
    """Setup Qdrant vector database."""
    # Download Qdrant
    print("Checking Qdrant download...")
    downloaded = download_qdrant()
    ipc_.set("qdrant_downloaded", downloaded)
    
    if not downloaded:
        print("Qdrant download failed")
        ipc_.set("qdrant_ready", False)
        return None
    
    # Check if already running
    if is_port_open("localhost", 45215):
        print("Qdrant already running")
        ipc_.set("qdrant_ready", True)
        return None
    
    # Start Qdrant server
    try:
        print("Starting Qdrant server...")
        qdrant_process = start_qdrant_server()
        
        # Wait for it to be ready
        for _ in range(30):  # 30 second timeout
            if is_port_open("localhost", 45215):
                ipc_.set("qdrant_ready", True)
                print("Qdrant server ready")
                return qdrant_process
            time.sleep(1)
        
        print("Qdrant server failed to start")
        ipc_.set("qdrant_ready", False)
        return qdrant_process
        
    except Exception as e:
        print(f"Qdrant setup failed: {e}")
        ipc_.set("qdrant_ready", False)
        return None

def start_servers():
    """Start HTTP and WebSocket servers."""
    processes = []
    
    # Start HTTP server with multiple workers
    try:
        http_process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "http_server:app",
            "--host", "127.0.0.1",
            "--port", "45213",
            "--workers", "4"
        ])
        processes.append(http_process)
        print(f"Started HTTP server with 4 workers on port 45213 (PID: {http_process.pid})")
    except Exception as e:
        print(f"Failed to start HTTP server: {e}")
    
    # Start WebSocket server (single worker) on different port
    try:
        ws_process = subprocess.Popen([
            sys.executable, "-m", "uvicorn",
            "websocket_server:app", 
            "--host", "127.0.0.1",
            "--port", "45214"
        ])
        processes.append(ws_process)
        print(f"Started WebSocket server on port 45214 (PID: {ws_process.pid})")
    except Exception as e:
        print(f"Failed to start WebSocket server: {e}")
    
    return processes

def startup_initialization():
    """Perform startup initialization for the session management system."""
    perform_startup_configuration()
    session = read_session()

    if session.strip() != "":
        print(f"Session found: {session}")
        refreshed_session = session_refresh(session)
        ipc_.set("current_session", refreshed_session)
    else:
        print("No session found")




# Main execution
if __name__ == "__main__":
    print("Initializing system...")

    startup_initialization()

    # Check services
    check_ollama()
    check_internet()

    # Setup Qdrant
    qdrant_process = setup_qdrant()

    # Check CUDA (the module does the check internally)
    try:
        import BASE.utils.check_cuda
        print("CUDA check completed")
    except Exception as e:
        print(f"CUDA check failed: {e}")

    # Start servers
    server_processes = start_servers()
    
    # Keep main process alive
    try:
        print("System ready. Press Ctrl+C to exit.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down...")
        
        # Clean up processes
        for process in server_processes:
            if process.poll() is None:
                process.terminate()
        
        if qdrant_process and qdrant_process.poll() is None:
            qdrant_process.terminate()
        
        print("Goodbye!")
